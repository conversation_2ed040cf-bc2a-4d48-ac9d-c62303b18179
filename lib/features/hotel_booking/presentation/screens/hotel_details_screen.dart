import 'package:flutter/material.dart';

class HotelDetailsScreen extends StatelessWidget {
  final String hotelId;

  const HotelDetailsScreen({Key? key, required this.hotelId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // In a real app, you would fetch hotel details using the hotelId
    return Scaffold(
      appBar: AppBar(title: const Text('Hotel Details')),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hotel image
            Container(
              height: 250,
              width: double.infinity,
              color: Colors.grey.shade300,
              child: Center(
                child: Icon(Icons.hotel, size: 80, color: Colors.grey.shade700),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hotel name and rating
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          'Grand Hotel Example',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: const [
                            Text(
                              '4.5',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Icon(Icons.star, size: 16, color: Colors.white),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Location
                  Row(
                    children: const [
                      Icon(Icons.location_on, color: Colors.grey, size: 16),
                      SizedBox(width: 4),
                      Text(
                        'Downtown, Example City',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Price
                  Row(
                    children: [
                      Text(
                        '\$120',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Text(' / night'),
                    ],
                  ),

                  const Divider(height: 32),

                  // Amenities
                  Text(
                    'Amenities',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    children: const [
                      AmenityItem(icon: Icons.wifi, label: 'Free WiFi'),
                      AmenityItem(
                        icon: Icons.ac_unit,
                        label: 'Air Conditioning',
                      ),
                      AmenityItem(icon: Icons.pool, label: 'Swimming Pool'),
                      AmenityItem(
                        icon: Icons.fitness_center,
                        label: 'Fitness Center',
                      ),
                      AmenityItem(icon: Icons.restaurant, label: 'Restaurant'),
                      AmenityItem(icon: Icons.local_parking, label: 'Parking'),
                    ],
                  ),

                  const Divider(height: 32),

                  // Description
                  Text(
                    'Description',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'This luxurious hotel offers comfortable rooms with modern amenities. '
                    'Located in the heart of the city, it provides easy access to major attractions, '
                    'shopping centers, and restaurants. Guests can enjoy the outdoor swimming pool, '
                    'fitness center, and on-site dining options.',
                    style: TextStyle(height: 1.5),
                  ),

                  const SizedBox(height: 24),

                  // Book now button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Implement booking functionality
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Booking functionality to be implemented',
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('BOOK NOW'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AmenityItem extends StatelessWidget {
  final IconData icon;
  final String label;

  const AmenityItem({Key? key, required this.icon, required this.label})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }
}
