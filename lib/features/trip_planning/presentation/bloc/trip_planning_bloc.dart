import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/repositories/trip_planning_repository.dart';
import 'trip_planning_event.dart';
import 'trip_planning_state.dart';

class TripPlanningBloc extends Bloc<TripPlanningEvent, TripPlanningState> {
  final TripPlanningRepository _repository;
  final LoggerService _logger = LoggerService();

  TripPlanningBloc({required TripPlanningRepository repository})
    : _repository = repository,
      super(TripPlanningInitial()) {
    on<GenerateTripPlanEvent>(_onGenerateTripPlan);
    on<LoadSavedTripPlansEvent>(_onLoadSavedTripPlans);
    on<SaveTripPlanEvent>(_onSaveTripPlan);
    on<DeleteTripPlanEvent>(_onDeleteTripPlan);
    on<LoadTripPlanDetailsEvent>(_onLoadTripPlanDetails);
  }

  Future<void> _onGenerateTripPlan(
    GenerateTripPlanEvent event,
    Emitter<TripPlanningState> emit,
  ) async {
    try {
      emit(TripPlanGenerating());

      final tripPlan = await _repository.generateTripPlan(
        destination: event.destination,
        startDate: event.startDate,
        endDate: event.endDate,
        budget: event.budget,
        preferences: event.preferences,
      );

      // Save the trip plan to the repository so it can be retrieved later
      await _repository.saveTripPlan(tripPlan);

      emit(TripPlanGenerated(tripPlan: tripPlan));
    } catch (e) {
      _logger.error('Error generating trip plan: $e');
      emit(TripPlanningError(message: 'Failed to generate trip plan: $e'));
    }
  }

  Future<void> _onLoadSavedTripPlans(
    LoadSavedTripPlansEvent event,
    Emitter<TripPlanningState> emit,
  ) async {
    try {
      emit(TripPlanningLoading());

      final tripPlans = await _repository.getSavedTripPlans();

      emit(SavedTripPlansLoaded(tripPlans: tripPlans));
    } catch (e) {
      _logger.error('Error loading saved trip plans: $e');
      emit(TripPlanningError(message: 'Failed to load saved trip plans: $e'));
    }
  }

  Future<void> _onSaveTripPlan(
    SaveTripPlanEvent event,
    Emitter<TripPlanningState> emit,
  ) async {
    try {
      emit(TripPlanningLoading());

      await _repository.saveTripPlan(event.tripPlan);

      emit(TripPlanSaved(tripPlan: event.tripPlan));

      // Reload saved trip plans
      add(LoadSavedTripPlansEvent());
    } catch (e) {
      _logger.error('Error saving trip plan: $e');
      emit(TripPlanningError(message: 'Failed to save trip plan: $e'));
    }
  }

  Future<void> _onDeleteTripPlan(
    DeleteTripPlanEvent event,
    Emitter<TripPlanningState> emit,
  ) async {
    try {
      emit(TripPlanningLoading());

      await _repository.deleteTripPlan(event.tripPlanId);

      emit(TripPlanDeleted(tripPlanId: event.tripPlanId));

      // Reload saved trip plans
      add(LoadSavedTripPlansEvent());
    } catch (e) {
      _logger.error('Error deleting trip plan: $e');
      emit(TripPlanningError(message: 'Failed to delete trip plan: $e'));
    }
  }

  Future<void> _onLoadTripPlanDetails(
    LoadTripPlanDetailsEvent event,
    Emitter<TripPlanningState> emit,
  ) async {
    try {
      emit(TripPlanningLoading());

      final tripPlan = await _repository.getTripPlanById(event.tripPlanId);

      if (tripPlan != null) {
        emit(TripPlanDetailsLoaded(tripPlan: tripPlan));
      } else {
        emit(const TripPlanningError(message: 'Trip plan not found'));
      }
    } catch (e) {
      _logger.error('Error loading trip plan details: $e');
      emit(TripPlanningError(message: 'Failed to load trip plan details: $e'));
    }
  }
}
