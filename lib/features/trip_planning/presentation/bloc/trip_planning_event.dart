import 'package:equatable/equatable.dart';
import '../../domain/models/trip_plan.dart';

abstract class TripPlanningEvent extends Equatable {
  const TripPlanningEvent();

  @override
  List<Object?> get props => [];
}

class GenerateTripPlanEvent extends TripPlanningEvent {
  final String destination;
  final DateTime startDate;
  final DateTime endDate;
  final int budget;
  final List<String> preferences;

  const GenerateTripPlanEvent({
    required this.destination,
    required this.startDate,
    required this.endDate,
    required this.budget,
    required this.preferences,
  });

  @override
  List<Object?> get props => [
    destination,
    startDate,
    endDate,
    budget,
    preferences,
  ];
}

class LoadSavedTripPlansEvent extends TripPlanningEvent {}

class SaveTripPlanEvent extends TripPlanningEvent {
  final TripPlan tripPlan;

  const SaveTripPlanEvent({required this.tripPlan});

  @override
  List<Object?> get props => [tripPlan];
}

class DeleteTripPlanEvent extends TripPlanningEvent {
  final String tripPlanId;

  const DeleteTripPlanEvent({required this.tripPlanId});

  @override
  List<Object?> get props => [tripPlanId];
}

class LoadTripPlanDetailsEvent extends TripPlanningEvent {
  final String tripPlanId;

  const LoadTripPlanDetailsEvent({required this.tripPlanId});

  @override
  List<Object?> get props => [tripPlanId];
}
