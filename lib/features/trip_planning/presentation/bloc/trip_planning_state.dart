import 'package:equatable/equatable.dart';
import '../../domain/models/trip_plan.dart';

abstract class TripPlanningState extends Equatable {
  const TripPlanningState();

  @override
  List<Object?> get props => [];
}

class TripPlanningInitial extends TripPlanningState {}

class TripPlanningLoading extends TripPlanningState {}

class TripPlanGenerating extends TripPlanningState {}

class TripPlanGenerated extends TripPlanningState {
  final TripPlan tripPlan;

  const TripPlanGenerated({required this.tripPlan});

  @override
  List<Object?> get props => [tripPlan];
}

class TripPlanningError extends TripPlanningState {
  final String message;

  const TripPlanningError({required this.message});

  @override
  List<Object?> get props => [message];
}

class SavedTripPlansLoaded extends TripPlanningState {
  final List<TripPlan> tripPlans;

  const SavedTripPlansLoaded({required this.tripPlans});

  @override
  List<Object?> get props => [tripPlans];
}

class TripPlanDetailsLoaded extends TripPlanningState {
  final TripPlan tripPlan;

  const TripPlanDetailsLoaded({required this.tripPlan});

  @override
  List<Object?> get props => [tripPlan];
}

class TripPlanSaved extends TripPlanningState {
  final TripPlan tripPlan;

  const TripPlanSaved({required this.tripPlan});

  @override
  List<Object?> get props => [tripPlan];
}

class TripPlanDeleted extends TripPlanningState {
  final String tripPlanId;

  const TripPlanDeleted({required this.tripPlanId});

  @override
  List<Object?> get props => [tripPlanId];
}
