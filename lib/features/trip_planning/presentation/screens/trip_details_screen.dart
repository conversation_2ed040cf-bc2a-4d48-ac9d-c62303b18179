import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/error_view.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/models/trip_plan.dart';
import '../bloc/trip_planning_bloc.dart';
import '../bloc/trip_planning_event.dart';
import '../bloc/trip_planning_state.dart';

class TripDetailsScreen extends StatefulWidget {
  final String tripId;

  const TripDetailsScreen({super.key, required this.tripId});

  @override
  State<TripDetailsScreen> createState() => _TripDetailsScreenState();
}

class _TripDetailsScreenState extends State<TripDetailsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<TripPlanningBloc>().add(
      LoadTripPlanDetailsEvent(tripPlanId: widget.tripId),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trip Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Share functionality would be implemented here
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Sharing functionality coming soon'),
                ),
              );
            },
          ),
        ],
      ),
      body: BlocBuilder<TripPlanningBloc, TripPlanningState>(
        builder: (context, state) {
          if (state is TripPlanningLoading) {
            return const Center(child: LoadingIndicator());
          }

          if (state is TripPlanDetailsLoaded) {
            return _buildTripDetails(context, state.tripPlan);
          }

          if (state is TripPlanningError) {
            return ErrorView(
              message: "Couldn't load trip details: ${state.message}",
              onRetry: () {
                context.read<TripPlanningBloc>().add(
                  LoadTripPlanDetailsEvent(tripPlanId: widget.tripId),
                );
              },
            );
          }

          return const Center(child: Text('No trip details available'));
        },
      ),
      floatingActionButton: BlocBuilder<TripPlanningBloc, TripPlanningState>(
        builder: (context, state) {
          if (state is TripPlanDetailsLoaded) {
            return FloatingActionButton.extended(
              onPressed: () {
                if (state.tripPlan.isSaved) {
                  // Delete trip
                  context.read<TripPlanningBloc>().add(
                    DeleteTripPlanEvent(tripPlanId: state.tripPlan.id),
                  );
                  Navigator.pop(context);
                } else {
                  // Save trip
                  context.read<TripPlanningBloc>().add(
                    SaveTripPlanEvent(tripPlan: state.tripPlan),
                  );
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Trip saved successfully')),
                  );
                }
              },
              icon: Icon(
                state.tripPlan.isSaved ? Icons.delete : Icons.bookmark,
              ),
              label: Text(state.tripPlan.isSaved ? 'Delete Trip' : 'Save Trip'),
              backgroundColor:
                  state.tripPlan.isSaved
                      ? Colors.red
                      : Theme.of(context).primaryColor,
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildTripDetails(BuildContext context, TripPlan tripPlan) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trip header
          _buildTripHeader(context, tripPlan),
          const SizedBox(height: 24),

          // Trip summary
          _buildTripSummary(context, tripPlan),
          const SizedBox(height: 24),

          // Day by day itinerary
          Text(
            'Day by Day Itinerary',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildItinerary(context, tripPlan),
          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Find Flights',
                  onPressed: () {
                    // Navigate to flight search
                    GoRouter.of(context).go('/flight-search');
                  },
                  icon: Icons.flight,
                  type: ButtonType.outline,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomButton(
                  text: 'Find Hotels',
                  onPressed: () {
                    // Navigate to hotel search
                    GoRouter.of(context).go('/hotel-search');
                  },
                  icon: Icons.hotel,
                  type: ButtonType.outline,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          CustomButton(
            text: 'Modify Trip Plan',
            onPressed: () {
              // Navigate back to trip planning with pre-filled data
              Navigator.pop(context);
            },
            icon: Icons.edit,
            isFullWidth: true,
          ),
        ],
      ),
    );
  }

  Widget _buildTripHeader(BuildContext context, TripPlan tripPlan) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Destination
        Text(
          tripPlan.destination,
          style: Theme.of(context).textTheme.displaySmall,
        ),
        const SizedBox(height: 8),

        // Date range
        Row(
          children: [
            const Icon(Icons.calendar_today, size: 16),
            const SizedBox(width: 8),
            Text(
              '${DateFormat('MMM d, yyyy').format(tripPlan.startDate)} - ${DateFormat('MMM d, yyyy').format(tripPlan.endDate)}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
        const SizedBox(height: 4),

        // Duration
        Row(
          children: [
            const Icon(Icons.timelapse, size: 16),
            const SizedBox(width: 8),
            Text(
              '${tripPlan.durationInDays} days',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
        const SizedBox(height: 4),

        // Budget
        Row(
          children: [
            const Icon(Icons.attach_money, size: 16),
            const SizedBox(width: 8),
            Text(
              'Budget: \$${tripPlan.budget}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Preferences
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              tripPlan.preferences.map((preference) {
                return Chip(
                  label: Text(preference),
                  backgroundColor: Theme.of(
                    context,
                  ).primaryColor.withOpacity(0.1),
                  labelStyle: TextStyle(color: Theme.of(context).primaryColor),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildTripSummary(BuildContext context, TripPlan tripPlan) {
    // In a real app, this would show a summary of the trip
    // For now, we'll just show a placeholder
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Trip Summary', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Text(
              'This ${tripPlan.durationInDays}-day trip to ${tripPlan.destination} includes accommodations, activities, and recommendations based on your preferences: ${tripPlan.preferences.join(', ')}.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Total estimated cost: \$${tripPlan.budget}',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItinerary(BuildContext context, TripPlan tripPlan) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: tripPlan.days.length,
      itemBuilder: (context, index) {
        final day = tripPlan.days[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            initiallyExpanded: index == 0, // First day expanded by default
            title: Text(
              'Day ${day.dayNumber}: ${DateFormat('MMM d').format(tripPlan.startDate.add(Duration(days: day.dayNumber - 1)))}',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              day.summary,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      day.summary,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),

                    // Activities
                    ...day.activities.map((activity) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.circle, size: 12),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    activity.title,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                if (activity.time != null)
                                  Text(
                                    activity.time!,
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Padding(
                              padding: const EdgeInsets.only(left: 20.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    activity.description,
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                  if (activity.location != null) ...[
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        const Icon(Icons.location_on, size: 14),
                                        const SizedBox(width: 4),
                                        Text(
                                          activity.location!,
                                          style:
                                              Theme.of(
                                                context,
                                              ).textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                  ],
                                  if (activity.cost != null) ...[
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.attach_money,
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '\$${activity.cost!.toStringAsFixed(2)}',
                                          style:
                                              Theme.of(
                                                context,
                                              ).textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),

                    // Accommodation
                    if (day.accommodation != null) ...[
                      const Divider(),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(Icons.hotel),
                          const SizedBox(width: 8),
                          Text(
                            'Accommodation',
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        day.accommodation!,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
