import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../bloc/trip_planning_bloc.dart';
import '../bloc/trip_planning_event.dart';
import '../bloc/trip_planning_state.dart';
import '../widgets/preference_chip.dart';

class TripPlanningScreen extends StatefulWidget {
  const TripPlanningScreen({super.key});

  @override
  State<TripPlanningScreen> createState() => _TripPlanningScreenState();
}

class _TripPlanningScreenState extends State<TripPlanningScreen> {
  final _formKey = GlobalKey<FormState>();
  final _destinationController = TextEditingController();
  final _budgetController = TextEditingController();

  DateTime? _startDate;
  DateTime? _endDate;
  final List<String> _selectedPreferences = [];

  @override
  void dispose() {
    _destinationController.dispose();
    _budgetController.dispose();
    super.dispose();
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
        // If end date is before start date or not set, update it
        if (_endDate == null || _endDate!.isBefore(_startDate!)) {
          _endDate = _startDate!.add(const Duration(days: 3));
        }
      });
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _endDate ??
          (_startDate?.add(const Duration(days: 3)) ??
              DateTime.now().add(const Duration(days: 3))),
      firstDate: _startDate ?? DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  void _togglePreference(String preference) {
    setState(() {
      if (_selectedPreferences.contains(preference)) {
        _selectedPreferences.remove(preference);
      } else {
        _selectedPreferences.add(preference);
      }
    });
  }

  void _generateTripPlan() {
    if (_formKey.currentState!.validate() &&
        _startDate != null &&
        _endDate != null) {
      final int budget = int.tryParse(_budgetController.text) ?? 0;

      context.read<TripPlanningBloc>().add(
        GenerateTripPlanEvent(
          destination: _destinationController.text,
          startDate: _startDate!,
          endDate: _endDate!,
          budget: budget,
          preferences: _selectedPreferences,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Plan Your Trip')),
      body: BlocConsumer<TripPlanningBloc, TripPlanningState>(
        listener: (context, state) {
          if (state is TripPlanGenerated) {
            // Navigate to trip details screen
            // Use the AppConstants for the route path
            GoRouter.of(
              context,
            ).go('${AppConstants.routeTripDetails}/${state.tripPlan.id}');
          } else if (state is TripPlanningError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          }
        },
        builder: (context, state) {
          if (state is TripPlanGenerating) {
            return const LoadingOverlay(
              isLoading: true,
              loadingText: 'Generating your personalized trip plan...',
              child: SizedBox.expand(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Where do you want to go?',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _destinationController,
                    decoration: const InputDecoration(
                      labelText: 'Destination',
                      hintText: 'e.g., Paris, Tokyo, New York',
                      prefixIcon: Icon(Icons.location_on),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a destination';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  Text(
                    'When are you traveling?',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectStartDate(context),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'Start Date',
                              prefixIcon: Icon(Icons.calendar_today),
                            ),
                            child: Text(
                              _startDate == null
                                  ? 'Select Date'
                                  : DateFormat(
                                    'MMM d, yyyy',
                                  ).format(_startDate!),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectEndDate(context),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'End Date',
                              prefixIcon: Icon(Icons.calendar_today),
                            ),
                            child: Text(
                              _endDate == null
                                  ? 'Select Date'
                                  : DateFormat('MMM d, yyyy').format(_endDate!),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (_startDate == null || _endDate == null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        'Please select both start and end dates',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  const SizedBox(height: 24),

                  Text(
                    'What\'s your budget?',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _budgetController,
                    decoration: const InputDecoration(
                      labelText: 'Budget (USD)',
                      hintText: 'e.g., 1000',
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your budget';
                      }
                      if (int.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  Text(
                    'What are your preferences?',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        AppConstants.tripPreferences.map((preference) {
                          return PreferenceChip(
                            label: preference,
                            isSelected: _selectedPreferences.contains(
                              preference,
                            ),
                            onSelected:
                                (selected) => _togglePreference(preference),
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: 32),

                  CustomButton(
                    text: 'Generate Trip Plan',
                    onPressed: _generateTripPlan,
                    isFullWidth: true,
                    icon: Icons.explore,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
