import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

class TripPlan extends Equatable {
  final String id;
  final String destination;
  final DateTime startDate;
  final DateTime endDate;
  final int budget;
  final List<String> preferences;
  final List<TripDay> days;
  final bool isSaved;
  final DateTime createdAt;

  const TripPlan({
    required this.id,
    required this.destination,
    required this.startDate,
    required this.endDate,
    required this.budget,
    required this.preferences,
    required this.days,
    this.isSaved = false,
    required this.createdAt,
  });

  factory TripPlan.create({
    required String destination,
    required DateTime startDate,
    required DateTime endDate,
    required int budget,
    required List<String> preferences,
    required List<TripDay> days,
  }) {
    return TripPlan(
      id: const Uuid().v4(),
      destination: destination,
      startDate: startDate,
      endDate: endDate,
      budget: budget,
      preferences: preferences,
      days: days,
      createdAt: DateTime.now(),
    );
  }

  TripPlan copyWith({
    String? id,
    String? destination,
    DateTime? startDate,
    DateTime? endDate,
    int? budget,
    List<String>? preferences,
    List<TripDay>? days,
    bool? isSaved,
    DateTime? createdAt,
  }) {
    return TripPlan(
      id: id ?? this.id,
      destination: destination ?? this.destination,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      budget: budget ?? this.budget,
      preferences: preferences ?? this.preferences,
      days: days ?? this.days,
      isSaved: isSaved ?? this.isSaved,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  int get durationInDays => endDate.difference(startDate).inDays + 1;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'destination': destination,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'budget': budget,
      'preferences': preferences,
      'days': days.map((day) => day.toJson()).toList(),
      'isSaved': isSaved,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TripPlan.fromJson(Map<String, dynamic> json) {
    return TripPlan(
      id: json['id'],
      destination: json['destination'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      budget: json['budget'],
      preferences: List<String>.from(json['preferences']),
      days: (json['days'] as List).map((day) => TripDay.fromJson(day)).toList(),
      isSaved: json['isSaved'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  List<Object?> get props => [
    id,
    destination,
    startDate,
    endDate,
    budget,
    preferences,
    days,
    isSaved,
    createdAt,
  ];
}

class TripDay extends Equatable {
  final int dayNumber;
  final String summary;
  final List<TripActivity> activities;
  final String? accommodation;

  const TripDay({
    required this.dayNumber,
    required this.summary,
    required this.activities,
    this.accommodation,
  });

  TripDay copyWith({
    int? dayNumber,
    String? summary,
    List<TripActivity>? activities,
    String? accommodation,
  }) {
    return TripDay(
      dayNumber: dayNumber ?? this.dayNumber,
      summary: summary ?? this.summary,
      activities: activities ?? this.activities,
      accommodation: accommodation ?? this.accommodation,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dayNumber': dayNumber,
      'summary': summary,
      'activities': activities.map((activity) => activity.toJson()).toList(),
      'accommodation': accommodation,
    };
  }

  factory TripDay.fromJson(Map<String, dynamic> json) {
    return TripDay(
      dayNumber: json['dayNumber'],
      summary: json['summary'],
      activities:
          (json['activities'] as List)
              .map((activity) => TripActivity.fromJson(activity))
              .toList(),
      accommodation: json['accommodation'],
    );
  }

  @override
  List<Object?> get props => [dayNumber, summary, activities, accommodation];
}

class TripActivity extends Equatable {
  final String title;
  final String description;
  final String? time;
  final String? location;
  final double? cost;
  final String? imageUrl;

  const TripActivity({
    required this.title,
    required this.description,
    this.time,
    this.location,
    this.cost,
    this.imageUrl,
  });

  TripActivity copyWith({
    String? title,
    String? description,
    String? time,
    String? location,
    double? cost,
    String? imageUrl,
  }) {
    return TripActivity(
      title: title ?? this.title,
      description: description ?? this.description,
      time: time ?? this.time,
      location: location ?? this.location,
      cost: cost ?? this.cost,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'time': time,
      'location': location,
      'cost': cost,
      'imageUrl': imageUrl,
    };
  }

  factory TripActivity.fromJson(Map<String, dynamic> json) {
    return TripActivity(
      title: json['title'],
      description: json['description'],
      time: json['time'],
      location: json['location'],
      cost: json['cost'],
      imageUrl: json['imageUrl'],
    );
  }

  @override
  List<Object?> get props => [
    title,
    description,
    time,
    location,
    cost,
    imageUrl,
  ];
}
