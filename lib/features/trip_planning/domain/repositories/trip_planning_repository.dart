import '../models/trip_plan.dart';

abstract class TripPlanningRepository {
  Future<TripPlan> generateTripPlan({
    required String destination,
    required DateTime startDate,
    required DateTime endDate,
    required int budget,
    required List<String> preferences,
  });

  Future<List<TripPlan>> getSavedTripPlans();

  Future<void> saveTripPlan(TripPlan tripPlan);

  Future<void> deleteTripPlan(String tripPlanId);

  Future<TripPlan?> getTripPlanById(String tripPlanId);
}
