import 'dart:convert';
import 'package:ai_travel_planner/core/api/openrouter_api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/trip_plan.dart';
import '../../domain/repositories/trip_planning_repository.dart';

class TripPlanningRepositoryImpl implements TripPlanningRepository {
  final OpenRouterApiService _apiService;
  final LoggerService _logger = LoggerService();

  TripPlanningRepositoryImpl({required OpenRouterApiService apiService})
    : _apiService = apiService;

  @override
  Future<TripPlan> generateTripPlan({
    required String destination,
    required DateTime startDate,
    required DateTime endDate,
    required int budget,
    required List<String> preferences,
  }) async {
    try {
      final response = await _apiService.generateTripPlan(
        destination: destination,
        startDate: startDate.toIso8601String(),
        endDate: endDate.toIso8601String(),
        budget: budget,
        preferences: preferences,
      );

      // Parse the AI response to create a structured trip plan
      final aiResponse = response['choices'][0]['message']['content'];

      // This is a simplified example - in a real app, you would need more robust parsing
      // of the AI response to create a structured TripPlan object
      final List<TripDay> days = _parseTripDays(aiResponse, startDate, endDate);

      return TripPlan.create(
        destination: destination,
        startDate: startDate,
        endDate: endDate,
        budget: budget,
        preferences: preferences,
        days: days,
      );
    } catch (e) {
      _logger.error('Error generating trip plan: $e');
      throw Exception('Failed to generate trip plan: $e');
    }
  }

  List<TripDay> _parseTripDays(
    String aiResponse,
    DateTime startDate,
    DateTime endDate,
  ) {
    _logger.info('Parsing trip days from AI response');
    
    final List<TripDay> days = [];
    final int durationInDays = endDate.difference(startDate).inDays + 1;
    
    try {
      // Try to match "Day X:" or "Day X" patterns
      final RegExp dayRegex = RegExp(r'Day\s+(\d+)(?:\s*:)?', caseSensitive: false);
      final Iterable<Match> matches = dayRegex.allMatches(aiResponse);
      
      if (matches.isEmpty) {
        _logger.warning('No day patterns found in AI response. Creating default days.');
        // If no day patterns found, create default days
        return _createDefaultDays(durationInDays);
      }
      
      // Extract day blocks using the matches
      List<String> dayBlocks = [];
      for (int i = 0; i < matches.length; i++) {
        final Match match = matches.elementAt(i);
        final int start = match.end;
        final int end = i < matches.length - 1 ? matches.elementAt(i + 1).start : aiResponse.length;
        
        if (start < end) {
          final String dayContent = aiResponse.substring(start, end).trim();
          final int dayNumber = int.parse(match.group(1)!);
          dayBlocks.add('$dayNumber:$dayContent');
        }
      }
      
      // Process each day block
      for (String block in dayBlocks) {
        final parts = block.split(':');
        if (parts.length < 2) continue;
        
        final int dayNumber = int.tryParse(parts[0]) ?? 0;
        if (dayNumber < 1 || dayNumber > durationInDays) continue;
        
        final String content = parts[1].trim();
        final List<String> paragraphs = content.split(RegExp(r'\n\s*\n'));
        
        // Extract summary (first paragraph)
        String summary = paragraphs.isNotEmpty 
            ? paragraphs[0].replaceAll(RegExp(r'\n'), ' ').trim()
            : 'Day $dayNumber of your trip';
            
        // Extract activities and accommodation
        List<TripActivity> activities = [];
        String? accommodation;
        
        // Process remaining paragraphs for activities
        for (int i = 1; i < paragraphs.length; i++) {
          final String paragraph = paragraphs[i].trim();
          
          // Skip empty paragraphs
          if (paragraph.isEmpty) continue;
          
          // Check if this paragraph is about accommodation
          if (paragraph.toLowerCase().contains('accommodation') ||
              paragraph.toLowerCase().contains('hotel') ||
              paragraph.toLowerCase().contains('stay') ||
              paragraph.toLowerCase().contains('lodging')) {
            accommodation = paragraph;
            continue;
          }
          
          // Extract activity details
          final List<String> lines = paragraph.split('\n');
          String title = lines.isNotEmpty ? lines[0].trim() : 'Activity';
          
          // Remove common prefixes like "Morning:", "Afternoon:", etc.
          title = title.replaceFirst(RegExp(r'^(Morning|Afternoon|Evening|Night):\s*', caseSensitive: false), '');
          
          // Clean up the title
          title = title.replaceFirst(RegExp(r'^[-•*]\s*'), '');
          
          // Get description (remaining lines)
          String description = lines.length > 1 
              ? lines.sublist(1).join('\n').trim() 
              : 'Enjoy this activity in ${dayNumber == 1 ? "your first day" : "day $dayNumber"} of your trip.';
              
          // Extract location if present
          String? location;
          String? time;
          double? cost;
          
          // Look for location indicators
          final locationMatch = RegExp(r'at\s+([\w\s\',]+)', caseSensitive: false).firstMatch(paragraph);
          if (locationMatch != null) {
            location = locationMatch.group(1)?.trim();
          }
          
          // Look for time indicators
          final timeMatch = RegExp(r'(\d{1,2}(?::\d{2})?\s*(?:AM|PM|am|pm))', caseSensitive: false).firstMatch(paragraph);
          if (timeMatch != null) {
            time = timeMatch.group(1)?.trim();
          }
          
          // Look for cost indicators
          final costMatch = RegExp(r'\$(\d+(?:\.\d{2})?)', caseSensitive: false).firstMatch(paragraph);
          if (costMatch != null) {
            cost = double.tryParse(costMatch.group(1) ?? '0');
          }
          
          activities.add(TripActivity(
            title: title,
            description: description,
            location: location,
            time: time,
            cost: cost,
          ));
        }
        
        // If no activities were found, add a default one
        if (activities.isEmpty) {
          activities.add(TripActivity(
            title: 'Explore $summary',
            description: 'Enjoy your day in this beautiful destination.',
          ));
        }
        
        days.add(TripDay(
          dayNumber: dayNumber,
          summary: summary,
          activities: activities,
          accommodation: accommodation,
        ));
      }
      
      // Sort days by day number
      days.sort((a, b) => a.dayNumber.compareTo(b.dayNumber));
      
      // If we're missing days, fill them in
      if (days.length < durationInDays) {
        final Set<int> existingDayNumbers = days.map((day) => day.dayNumber).toSet();
        
        for (int i = 1; i <= durationInDays; i++) {
          if (!existingDayNumbers.contains(i)) {
            days.add(_createDefaultDay(i));
          }
        }
        
        // Sort again after adding missing days
        days.sort((a, b) => a.dayNumber.compareTo(b.dayNumber));
      }
      
    } catch (e) {
      _logger.error('Error parsing trip days: $e');
      // Return default days if parsing fails
      return _createDefaultDays(durationInDays);
    }
    
    _logger.info('Successfully parsed ${days.length} days');
    return days;
  }
  
  // Helper method to create a default day
  TripDay _createDefaultDay(int dayNumber) {
    return TripDay(
      dayNumber: dayNumber,
      summary: 'Day $dayNumber of your trip',
      activities: [
        TripActivity(
          title: 'Explore the destination',
          description: 'Enjoy your day at your own pace, exploring local attractions and cuisine.',
          time: 'All day',
        ),
        TripActivity(
          title: 'Local cuisine experience',
          description: 'Try out the local restaurants and cafes for an authentic experience.',
          time: 'Evening',
        ),
      ],
      accommodation: 'Accommodation for day $dayNumber',
    );
  }
  
  // Helper method to create default days for the entire trip
  List<TripDay> _createDefaultDays(int durationInDays) {
    final List<TripDay> days = [];
    
    for (int i = 1; i <= durationInDays; i++) {
      days.add(_createDefaultDay(i));
    }
    
    return days;
  }

  @override
  Future<List<TripPlan>> getSavedTripPlans() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? savedTripsJson = prefs.getString(
        AppConstants.keySavedTrips,
      );

      if (savedTripsJson == null) {
        return [];
      }

      final List<dynamic> savedTrips = jsonDecode(savedTripsJson);
      return savedTrips.map((trip) => TripPlan.fromJson(trip)).toList();
    } catch (e) {
      _logger.error('Error getting saved trip plans: $e');
      return [];
    }
  }

  @override
  Future<void> saveTripPlan(TripPlan tripPlan) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<TripPlan> savedTrips = await getSavedTripPlans();

      // Check if the trip plan already exists
      final int existingIndex = savedTrips.indexWhere(
        (trip) => trip.id == tripPlan.id,
      );

      if (existingIndex >= 0) {
        // Update existing trip plan
        savedTrips[existingIndex] = tripPlan.copyWith(isSaved: true);
      } else {
        // Add new trip plan
        savedTrips.add(tripPlan.copyWith(isSaved: true));
      }

      // Save to SharedPreferences
      await prefs.setString(
        AppConstants.keySavedTrips,
        jsonEncode(savedTrips.map((trip) => trip.toJson()).toList()),
      );
    } catch (e) {
      _logger.error('Error saving trip plan: $e');
      throw Exception('Failed to save trip plan: $e');
    }
  }

  @override
  Future<void> deleteTripPlan(String tripPlanId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<TripPlan> savedTrips = await getSavedTripPlans();

      // Remove the trip plan with the given ID
      savedTrips.removeWhere((trip) => trip.id == tripPlanId);

      // Save the updated list back to SharedPreferences
      await prefs.setString(
        AppConstants.keySavedTrips,
        jsonEncode(savedTrips.map((trip) => trip.toJson()).toList()),
      );
    } catch (e) {
      _logger.error('Error deleting trip plan: $e');
      throw Exception('Failed to delete trip plan: $e');
    }
  }

  @override
  Future<TripPlan?> getTripPlanById(String tripPlanId) async {
    try {
      final List<TripPlan> savedTrips = await getSavedTripPlans();

      // Log the number of saved trips and their IDs for debugging
      _logger.info('Found ${savedTrips.length} saved trips');
      for (var trip in savedTrips) {
        _logger.info('Saved trip ID: ${trip.id}');
      }

      // Check if the requested trip ID exists
      _logger.info('Looking for trip with ID: $tripPlanId');

      final tripPlan = savedTrips.firstWhere(
        (trip) => trip.id == tripPlanId,
        orElse: () => throw Exception('Trip plan not found'),
      );

      _logger.info('Found trip plan: ${tripPlan.destination}');
      return tripPlan;
    } catch (e) {
      _logger.error('Error getting trip plan by ID: $e');
      return null;
    }
  }
}