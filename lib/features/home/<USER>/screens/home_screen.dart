import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../trip_planning/presentation/bloc/trip_planning_bloc.dart';
import '../../../trip_planning/presentation/bloc/trip_planning_event.dart';
import '../../../trip_planning/presentation/bloc/trip_planning_state.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load saved trip plans when the home screen is initialized
    context.read<TripPlanningBloc>().add(LoadSavedTripPlansEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Travel Planner'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              GoRouter.of(context).go(AppConstants.routeSettings);
            },
          ),
        ],
      ),
      body: BlocBuilder<TripPlanningBloc, TripPlanningState>(
        builder: (context, state) {
          if (state is TripPlanningLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is SavedTripPlansLoaded) {
            return _buildHomeContent(context, state.tripPlans.isEmpty);
          }

          return _buildHomeContent(context, true);
        },
      ),
    );
  }

  Widget _buildHomeContent(BuildContext context, bool noSavedTrips) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section
          Text(
            'Welcome to AI Travel Planner',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Plan your perfect trip with the help of AI',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 24),

          // Quick actions
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quick Actions',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: 'Plan a New Trip',
                    onPressed: () {
                      GoRouter.of(context).go(AppConstants.routeTripPlanning);
                    },
                    icon: Icons.add,
                    isFullWidth: true,
                  ),
                  const SizedBox(height: 12),
                  CustomButton(
                    text: 'Search Flights',
                    onPressed: () {
                      GoRouter.of(context).go(AppConstants.routeFlightSearch);
                    },
                    icon: Icons.flight,
                    isFullWidth: true,
                    type: ButtonType.outline,
                  ),
                  const SizedBox(height: 12),
                  CustomButton(
                    text: 'Find Hotels',
                    onPressed: () {
                      GoRouter.of(context).go(AppConstants.routeHotelSearch);
                    },
                    icon: Icons.hotel,
                    isFullWidth: true,
                    type: ButtonType.outline,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Saved trips or recommendations
          BlocBuilder<TripPlanningBloc, TripPlanningState>(
            builder: (context, state) {
              if (state is SavedTripPlansLoaded && state.tripPlans.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Your Saved Trips',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        TextButton(
                          onPressed: () {
                            // Navigate to a view all saved trips screen
                            // This would be implemented in a real app
                          },
                          child: const Text('View All'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount:
                            state.tripPlans.length > 3
                                ? 3
                                : state.tripPlans.length,
                        itemBuilder: (context, index) {
                          final trip = state.tripPlans[index];
                          return Container(
                            width: 250,
                            margin: const EdgeInsets.only(right: 16),
                            child: Card(
                              clipBehavior: Clip.antiAlias,
                              child: InkWell(
                                onTap: () {
                                  GoRouter.of(context).go(
                                    '${AppConstants.routeTripDetails}/${trip.id}',
                                  );
                                },
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      height: 100,
                                      color: Theme.of(
                                        context,
                                      ).primaryColor.withOpacity(0.2),
                                      child: Center(
                                        child: Icon(
                                          Icons.landscape,
                                          size: 48,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            trip.destination,
                                            style:
                                                Theme.of(
                                                  context,
                                                ).textTheme.titleMedium,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '${trip.startDate.day}/${trip.startDate.month}/${trip.startDate.year} - ${trip.endDate.day}/${trip.endDate.month}/${trip.endDate.year}',
                                            style:
                                                Theme.of(
                                                  context,
                                                ).textTheme.bodySmall,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                );
              } else {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Popular Destinations',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ListView(
                        scrollDirection: Axis.horizontal,
                        children: [
                          _buildDestinationCard(
                            context,
                            'Paris',
                            'The City of Light',
                            Icons.location_city,
                          ),
                          _buildDestinationCard(
                            context,
                            'Tokyo',
                            'Modern meets traditional',
                            Icons.location_city,
                          ),
                          _buildDestinationCard(
                            context,
                            'New York',
                            'The Big Apple',
                            Icons.location_city,
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }
            },
          ),
          const SizedBox(height: 24),

          // Travel tips
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Travel Tips',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  const ListTile(
                    leading: Icon(Icons.lightbulb_outline),
                    title: Text('Pack light and smart'),
                    subtitle: Text(
                      'Roll clothes instead of folding to save space',
                    ),
                  ),
                  const Divider(),
                  const ListTile(
                    leading: Icon(Icons.security),
                    title: Text('Stay safe while traveling'),
                    subtitle: Text(
                      'Keep digital copies of important documents',
                    ),
                  ),
                  const Divider(),
                  const ListTile(
                    leading: Icon(Icons.savings),
                    title: Text('Save on accommodations'),
                    subtitle: Text(
                      'Consider booking mid-week for better rates',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationCard(
    BuildContext context,
    String name,
    String description,
    IconData icon,
  ) {
    return Container(
      width: 250,
      margin: const EdgeInsets.only(right: 16),
      child: Card(
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: () {
            // Pre-fill the trip planning form with this destination
            GoRouter.of(context).go(AppConstants.routeTripPlanning);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 100,
                color: Theme.of(context).primaryColor.withOpacity(0.2),
                child: Center(
                  child: Icon(
                    icon,
                    size: 48,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(name, style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
