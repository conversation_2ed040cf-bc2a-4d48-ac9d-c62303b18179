import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              GoRouter.of(context).go(AppConstants.routeSettings);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile header
            Center(
              child: Column(
                children: [
                  const CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.grey,
                    child: Icon(Icons.person, size: 50, color: Colors.white),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '<PERSON>',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Text('<EMAIL>'),
                  const SizedBox(height: 8),
                  OutlinedButton(
                    onPressed: () {
                      // Edit profile functionality
                    },
                    child: const Text('Edit Profile'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
            const Divider(),

            // Stats section
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildStatItem(context, '12', 'Trips'),
                  _buildStatItem(context, '4', 'Upcoming'),
                  _buildStatItem(context, '8', 'Completed'),
                ],
              ),
            ),

            const Divider(),
            const SizedBox(height: 16),

            // Upcoming trips
            Text(
              'Upcoming Trips',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            // Trip cards
            _buildTripCard(
              context,
              'Paris Getaway',
              'May 15 - May 22, 2023',
              Icons.flight_takeoff,
            ),
            _buildTripCard(
              context,
              'Tokyo Adventure',
              'June 10 - June 20, 2023',
              Icons.flight_takeoff,
            ),

            const SizedBox(height: 24),

            // View all trips button
            Center(
              child: OutlinedButton(
                onPressed: () {
                  // Navigate to all trips
                },
                child: const Text('View All Trips'),
              ),
            ),

            const SizedBox(height: 24),
            const Divider(),

            // Loyalty section
            ListTile(
              leading: const Icon(Icons.card_membership),
              title: const Text('Loyalty Program'),
              subtitle: const Text('Gold Member'),
              trailing: const Text('2,450 pts'),
              onTap: () {
                // Navigate to loyalty program details
              },
            ),

            // Support section
            ListTile(
              leading: const Icon(Icons.support_agent),
              title: const Text('Customer Support'),
              onTap: () {
                // Navigate to customer support
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        Text(label, style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  Widget _buildTripCard(
    BuildContext context,
    String destination,
    String dates,
    IconData icon,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
          child: Icon(icon, color: Theme.of(context).primaryColor),
        ),
        title: Text(
          destination,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(dates),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          // Navigate to trip details
        },
      ),
    );
  }
}
