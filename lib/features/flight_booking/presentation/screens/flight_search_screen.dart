import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/custom_button.dart';

class FlightSearchScreen extends StatefulWidget {
  const FlightSearchScreen({super.key});

  @override
  State<FlightSearchScreen> createState() => _FlightSearchScreenState();
}

class _FlightSearchScreenState extends State<FlightSearchScreen> {
  final _formKey = GlobalKey<FormState>();
  final _originController = TextEditingController();
  final _destinationController = TextEditingController();

  DateTime? _departureDate;
  DateTime? _returnDate;
  int _passengerCount = 1;
  String _selectedCabinClass = AppConstants.cabinClasses[0];
  bool _isRoundTrip = true;

  @override
  void dispose() {
    _originController.dispose();
    _destinationController.dispose();
    super.dispose();
  }

  Future<void> _selectDepartureDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _departureDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _departureDate) {
      setState(() {
        _departureDate = picked;
        // If return date is before departure date or not set, update it
        if (_returnDate == null || _returnDate!.isBefore(_departureDate!)) {
          _returnDate = _departureDate!.add(const Duration(days: 7));
        }
      });
    }
  }

  Future<void> _selectReturnDate(BuildContext context) async {
    if (_departureDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a departure date first')),
      );
      return;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _returnDate ?? _departureDate!.add(const Duration(days: 7)),
      firstDate: _departureDate!,
      lastDate: _departureDate!.add(const Duration(days: 365)),
    );
    if (picked != null && picked != _returnDate) {
      setState(() {
        _returnDate = picked;
      });
    }
  }

  void _searchFlights() {
    if (_formKey.currentState!.validate() && _departureDate != null) {
      if (_isRoundTrip && _returnDate == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a return date')),
        );
        return;
      }

      // In a real app, this would call a flight search service
      // For now, just navigate to a mock results page
      GoRouter.of(
        context,
      ).go('${AppConstants.routeFlightDetails}/mock-flight-id');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Flight Search')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip type selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: RadioListTile<bool>(
                          title: const Text('Round Trip'),
                          value: true,
                          groupValue: _isRoundTrip,
                          onChanged: (value) {
                            setState(() {
                              _isRoundTrip = value!;
                            });
                          },
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<bool>(
                          title: const Text('One Way'),
                          value: false,
                          groupValue: _isRoundTrip,
                          onChanged: (value) {
                            setState(() {
                              _isRoundTrip = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Origin and destination
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _originController,
                      decoration: const InputDecoration(
                        labelText: 'From',
                        hintText: 'City or Airport',
                        prefixIcon: Icon(Icons.flight_takeoff),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter origin';
                        }
                        return null;
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.swap_horiz),
                    onPressed: () {
                      final temp = _originController.text;
                      _originController.text = _destinationController.text;
                      _destinationController.text = temp;
                    },
                  ),
                  Expanded(
                    child: TextFormField(
                      controller: _destinationController,
                      decoration: const InputDecoration(
                        labelText: 'To',
                        hintText: 'City or Airport',
                        prefixIcon: Icon(Icons.flight_land),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter destination';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Dates
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectDepartureDate(context),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Departure Date',
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          _departureDate == null
                              ? 'Select Date'
                              : DateFormat(
                                'MMM d, yyyy',
                              ).format(_departureDate!),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child:
                        _isRoundTrip
                            ? InkWell(
                              onTap: () => _selectReturnDate(context),
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'Return Date',
                                  prefixIcon: Icon(Icons.calendar_today),
                                ),
                                child: Text(
                                  _returnDate == null
                                      ? 'Select Date'
                                      : DateFormat(
                                        'MMM d, yyyy',
                                      ).format(_returnDate!),
                                ),
                              ),
                            )
                            : Container(),
                  ),
                ],
              ),
              if (_departureDate == null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'Please select a departure date',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                      fontSize: 12,
                    ),
                  ),
                ),
              const SizedBox(height: 24),

              // Passengers and cabin class
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: 'Passengers',
                        prefixIcon: Icon(Icons.person),
                      ),
                      value: _passengerCount,
                      items:
                          List.generate(9, (index) => index + 1)
                              .map(
                                (count) => DropdownMenuItem<int>(
                                  value: count,
                                  child: Text(
                                    '$count ${count == 1 ? 'Passenger' : 'Passengers'}',
                                  ),
                                ),
                              )
                              .toList(),
                      onChanged: (value) {
                        setState(() {
                          _passengerCount = value!;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Cabin Class',
                        prefixIcon: Icon(Icons.airline_seat_recline_normal),
                      ),
                      value: _selectedCabinClass,
                      items:
                          AppConstants.cabinClasses
                              .map(
                                (cabinClass) => DropdownMenuItem<String>(
                                  value: cabinClass,
                                  child: Text(cabinClass),
                                ),
                              )
                              .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCabinClass = value!;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Search button
              CustomButton(
                text: 'Search Flights',
                onPressed: _searchFlights,
                isFullWidth: true,
                icon: Icons.search,
              ),
              const SizedBox(height: 24),

              // Recent searches
              Text(
                'Recent Searches',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Card(
                child: ListView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    ListTile(
                      leading: const Icon(Icons.history),
                      title: const Text('New York → London'),
                      subtitle: const Text(
                        'Jun 15 - Jun 22 • 1 Passenger • Economy',
                      ),
                      onTap: () {
                        // Pre-fill the form with this search
                        setState(() {
                          _originController.text = 'New York';
                          _destinationController.text = 'London';
                          _departureDate = DateTime(2023, 6, 15);
                          _returnDate = DateTime(2023, 6, 22);
                          _passengerCount = 1;
                          _selectedCabinClass = 'Economy';
                          _isRoundTrip = true;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.history),
                      title: const Text('San Francisco → Tokyo'),
                      subtitle: const Text(
                        'Jul 10 - Jul 24 • 2 Passengers • Business',
                      ),
                      onTap: () {
                        // Pre-fill the form with this search
                        setState(() {
                          _originController.text = 'San Francisco';
                          _destinationController.text = 'Tokyo';
                          _departureDate = DateTime(2023, 7, 10);
                          _returnDate = DateTime(2023, 7, 24);
                          _passengerCount = 2;
                          _selectedCabinClass = 'Business';
                          _isRoundTrip = true;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
