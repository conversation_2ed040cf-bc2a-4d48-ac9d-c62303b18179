import 'package:flutter/material.dart';
import '../../../../core/widgets/custom_button.dart';

class FlightDetailsScreen extends StatelessWidget {
  final String flightId;

  const FlightDetailsScreen({super.key, required this.flightId});

  @override
  Widget build(BuildContext context) {
    // In a real app, this would fetch flight details from an API
    // For now, we'll use mock data
    return Scaffold(
      appBar: AppBar(title: const Text('Flight Details')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Flight summary card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'New York (JFK) → London (LHR)',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const Text(
                          '\$750',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Round Trip • Economy • 1 Passenger',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const Divider(height: 24),

                    // Outbound flight
                    Row(
                      children: [
                        const Icon(Icons.flight_takeoff),
                        const SizedBox(width: 8),
                        Text(
                          'Outbound Flight',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'JFK',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text(
                              '10:00 AM',
                              style: TextStyle(fontSize: 16),
                            ),
                            Text(
                              'Jun 15, 2023',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text(
                              '7h 30m',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              width: 100,
                              height: 2,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'Direct',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            const Text(
                              'LHR',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text(
                              '5:30 PM',
                              style: TextStyle(fontSize: 16),
                            ),
                            Text(
                              'Jun 15, 2023',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'British Airways • BA178',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),

                    // Return flight
                    Row(
                      children: [
                        const Icon(Icons.flight_land),
                        const SizedBox(width: 8),
                        Text(
                          'Return Flight',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'LHR',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text(
                              '2:15 PM',
                              style: TextStyle(fontSize: 16),
                            ),
                            Text(
                              'Jun 22, 2023',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text(
                              '8h 05m',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              width: 100,
                              height: 2,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'Direct',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            const Text(
                              'JFK',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text(
                              '5:20 PM',
                              style: TextStyle(fontSize: 16),
                            ),
                            Text(
                              'Jun 22, 2023',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'British Airways • BA183',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Flight details
            Text(
              'Flight Details',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),

            // Outbound details
            Card(
              child: ExpansionTile(
                title: const Text('Outbound Flight Details'),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                'https://via.placeholder.com/50',
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'British Airways',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Flight BA178',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const Row(
                          children: [
                            Icon(Icons.airplanemode_active, size: 16),
                            SizedBox(width: 8),
                            Text('Aircraft: Boeing 777-300ER'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.airline_seat_recline_normal, size: 16),
                            SizedBox(width: 8),
                            Text('Seat: Economy, 34" seat pitch'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.luggage, size: 16),
                            SizedBox(width: 8),
                            Text('Baggage: 1 x 23kg checked, 1 x 10kg cabin'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.wifi, size: 16),
                            SizedBox(width: 8),
                            Text('In-flight: Wi-Fi available (paid)'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.fastfood, size: 16),
                            SizedBox(width: 8),
                            Text('Meal: Complimentary meal and beverages'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Return details
            Card(
              child: ExpansionTile(
                title: const Text('Return Flight Details'),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                'https://via.placeholder.com/50',
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'British Airways',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Flight BA183',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const Row(
                          children: [
                            Icon(Icons.airplanemode_active, size: 16),
                            SizedBox(width: 8),
                            Text('Aircraft: Boeing 777-300ER'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.airline_seat_recline_normal, size: 16),
                            SizedBox(width: 8),
                            Text('Seat: Economy, 34" seat pitch'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.luggage, size: 16),
                            SizedBox(width: 8),
                            Text('Baggage: 1 x 23kg checked, 1 x 10kg cabin'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.wifi, size: 16),
                            SizedBox(width: 8),
                            Text('In-flight: Wi-Fi available (paid)'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          children: [
                            Icon(Icons.fastfood, size: 16),
                            SizedBox(width: 8),
                            Text('Meal: Complimentary meal and beverages'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Price breakdown
            Text(
              'Price Breakdown',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Base fare (1 passenger)'),
                        Text('\$650.00'),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [Text('Taxes and fees'), Text('\$100.00')],
                    ),
                    const Divider(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '\$750.00',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Booking buttons
            CustomButton(
              text: 'Book Now',
              onPressed: () {
                // Booking functionality would be implemented here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Booking functionality coming soon'),
                  ),
                );
              },
              isFullWidth: true,
            ),
            const SizedBox(height: 16),
            CustomButton(
              text: 'Save to Trip',
              onPressed: () {
                // Save to trip functionality would be implemented here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Flight saved to your trip')),
                );
                Navigator.pop(context);
              },
              isFullWidth: true,
              type: ButtonType.outline,
            ),
          ],
        ),
      ),
    );
  }
}
