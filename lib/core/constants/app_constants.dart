class AppConstants {
  // Routes
  static const String routeHome = '/';
  static const String routeLogin = '/login';
  static const String routeRegister = '/register';
  static const String routeTripPlanning = '/trip-planning';
  static const String routeTripDetails = '/trip-details';
  static const String routeFlightSearch = '/flight-search';
  static const String routeFlightDetails = '/flight-details';
  static const String routeHotelSearch = '/hotel-search';
  static const String routeHotelDetails = '/hotel-details';
  static const String routeProfile = '/profile';
  static const String routeSettings = '/settings';

  // Storage Keys
  static const String keyAuthToken = 'auth_token';
  static const String keyUserProfile = 'user_profile';
  static const String keyThemeMode = 'theme_mode';
  static const String keyRecentSearches = 'recent_searches';
  static const String keySavedTrips = 'saved_trips';

  // API Error Messages
  static const String errorGeneric = 'Something went wrong. Please try again.';
  static const String errorConnection =
      'No internet connection. Please check your network.';
  static const String errorTimeout = 'Request timed out. Please try again.';
  static const String errorAuthentication =
      'Authentication failed. Please login again.';

  // Trip Planning
  static const List<String> tripPreferences = [
    'Adventure',
    'Beach',
    'City',
    'Cultural',
    'Family-friendly',
    'Food',
    'Luxury',
    'Nature',
    'Nightlife',
    'Relaxation',
    'Romance',
    'Shopping',
    'Sightseeing',
    'Wellness',
  ];

  // Flight Booking
  static const List<String> cabinClasses = [
    'Economy',
    'Premium Economy',
    'Business',
    'First',
  ];

  // Hotel Booking
  static const List<String> hotelAmenities = [
    'Free WiFi',
    'Swimming Pool',
    'Fitness Center',
    'Spa',
    'Restaurant',
    'Room Service',
    'Parking',
    'Airport Shuttle',
    'Pet Friendly',
    'Business Center',
    'Breakfast Included',
    'Air Conditioning',
  ];
}
