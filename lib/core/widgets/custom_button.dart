import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

enum ButtonType { primary, secondary, outline, text }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final ButtonType type;
  final bool isLoading;
  final bool isFullWidth;
  final IconData? icon;
  final double? width;
  final double height;
  final double borderRadius;
  final EdgeInsets padding;
  final TextStyle? textStyle;

  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = ButtonType.primary,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.width,
    this.height = 48.0,
    this.borderRadius = 8.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Button style based on type
    ButtonStyle getButtonStyle() {
      switch (type) {
        case ButtonType.primary:
          return ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            padding: padding,
          );
        case ButtonType.secondary:
          return ElevatedButton.styleFrom(
            backgroundColor: AppTheme.secondaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            padding: padding,
          );
        case ButtonType.outline:
          return OutlinedButton.styleFrom(
            foregroundColor: AppTheme.primaryColor,
            side: const BorderSide(color: AppTheme.primaryColor),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            padding: padding,
          );
        case ButtonType.text:
          return TextButton.styleFrom(
            foregroundColor: AppTheme.primaryColor,
            padding: padding,
          );
      }
    }

    // Button content
    Widget buttonContent() {
      if (isLoading) {
        return SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              type == ButtonType.outline || type == ButtonType.text
                  ? AppTheme.primaryColor
                  : Colors.white,
            ),
          ),
        );
      }

      if (icon != null) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20),
            const SizedBox(width: 8),
            Text(
              text,
              style:
                  textStyle ??
                  theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ],
        );
      }

      return Text(
        text,
        style:
            textStyle ??
            theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
      );
    }

    // Button widget based on type
    Widget buttonWidget() {
      switch (type) {
        case ButtonType.primary:
        case ButtonType.secondary:
          return ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: getButtonStyle(),
            child: buttonContent(),
          );
        case ButtonType.outline:
          return OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: getButtonStyle(),
            child: buttonContent(),
          );
        case ButtonType.text:
          return TextButton(
            onPressed: isLoading ? null : onPressed,
            style: getButtonStyle(),
            child: buttonContent(),
          );
      }
    }

    return SizedBox(
      width: isFullWidth ? double.infinity : width,
      height: height,
      child: buttonWidget(),
    );
  }
}
