import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/app_config.dart';
import '../utils/logger.dart';

class OpenRouterApiService {
  final Dio _dio;
  final LoggerService _logger = LoggerService();

  OpenRouterApiService() : _dio = Dio() {
    _dio.options.baseUrl = AppConfig.openRouterBaseUrl;
    _dio.options.connectTimeout = Duration(
      seconds: AppConfig.apiTimeoutSeconds,
    );
    _dio.options.receiveTimeout = Duration(
      seconds: AppConfig.apiTimeoutSeconds,
    );
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${AppConfig.openRouterApiKey}',
    };

    // Add interceptors for logging in debug mode
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(requestBody: true, responseBody: true),
      );
    }
  }

  Future<Map<String, dynamic>> generateTripPlan({
    required String destination,
    required String startDate,
    required String endDate,
    required int budget,
    required List<String> preferences,
  }) async {
    try {
      final response = await _dio.post(
        AppConfig.chatCompletionEndpoint,
        data: {
          'model': AppConfig.openRouterModel,
          'messages': [
            {
              'role': 'system',
              'content':
                  'You are an AI travel assistant that creates detailed trip plans.',
            },
            {
              'role': 'user',
              'content':
                  'Create a trip plan for $destination from $startDate to $endDate '
                  'with a budget of \$$budget. My preferences are: ${preferences.join(", ")}.',
            },
          ],
          'temperature': 0.7,
          'max_tokens': 1000,
        },
      );

      return response.data;
    } catch (e) {
      _logger.error('Error generating trip plan: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> searchFlights({
    required String origin,
    required String destination,
    required String departureDate,
    String? returnDate,
    int? passengers,
    String? cabinClass,
  }) async {
    try {
      final response = await _dio.post(
        AppConfig.chatCompletionEndpoint,
        data: {
          'model': AppConfig.openRouterModel,
          'messages': [
            {
              'role': 'system',
              'content':
                  'You are an AI flight search assistant that provides flight options.',
            },
            {
              'role': 'user',
              'content':
                  'Find flights from $origin to $destination on $departureDate'
                  '${returnDate != null ? ' with return on $returnDate' : ''}'
                  '${passengers != null ? ' for $passengers passengers' : ''}'
                  '${cabinClass != null ? ' in $cabinClass class' : ''}.',
            },
          ],
          'temperature': 0.3,
          'max_tokens': 800,
        },
      );

      return response.data;
    } catch (e) {
      _logger.error('Error searching flights: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> searchHotels({
    required String location,
    required String checkInDate,
    required String checkOutDate,
    int? guests,
    int? rooms,
    List<String>? amenities,
  }) async {
    try {
      final response = await _dio.post(
        AppConfig.chatCompletionEndpoint,
        data: {
          'model': AppConfig.openRouterModel,
          'messages': [
            {
              'role': 'system',
              'content':
                  'You are an AI hotel search assistant that provides hotel options.',
            },
            {
              'role': 'user',
              'content':
                  'Find hotels in $location from $checkInDate to $checkOutDate'
                  '${guests != null ? ' for $guests guests' : ''}'
                  '${rooms != null ? ' with $rooms rooms' : ''}'
                  '${amenities != null && amenities.isNotEmpty ? ' with amenities: ${amenities.join(", ")}' : ''}.',
            },
          ],
          'temperature': 0.3,
          'max_tokens': 800,
        },
      );

      return response.data;
    } catch (e) {
      _logger.error('Error searching hotels: $e');
      rethrow;
    }
  }
}
