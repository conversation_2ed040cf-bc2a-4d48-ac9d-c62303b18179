import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  static String get openRouterApiKey => dotenv.env['OPENROUTER_API_KEY'] ?? '';
  static String get openRouterModel =>
      dotenv.env['OPENROUTER_MODEL'] ?? 'google/gemini-2.5-flash-preview-05-20';

  // API endpoints
  static const String openRouterBaseUrl = 'https://openrouter.ai/api/v1';
  static const String chatCompletionEndpoint = '/chat/completions';

  // App settings
  static const String appName = 'AI Travel Planner';
  static const int apiTimeoutSeconds = 30;
}
