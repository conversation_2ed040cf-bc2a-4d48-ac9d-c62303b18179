import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_constants.dart';

// Import screens (to be created)
import '../../features/home/<USER>/screens/home_screen.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/register_screen.dart';
import '../../features/trip_planning/presentation/screens/trip_planning_screen.dart';
import '../../features/trip_planning/presentation/screens/trip_details_screen.dart';
import '../../features/flight_booking/presentation/screens/flight_search_screen.dart';
import '../../features/flight_booking/presentation/screens/flight_details_screen.dart';
import '../../features/hotel_booking/presentation/screens/hotel_search_screen.dart';
import '../../features/hotel_booking/presentation/screens/hotel_details_screen.dart';
import '../../features/profile/presentation/screens/profile_screen.dart';
import '../../features/profile/presentation/screens/settings_screen.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    initialLocation: AppConstants.routeHome,
    navigatorKey: _rootNavigatorKey,
    debugLogDiagnostics: true,
    routes: [
      // Shell route for bottom navigation
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return ScaffoldWithNavBar(child: child);
        },
        routes: [
          // Home
          GoRoute(
            path: AppConstants.routeHome,
            builder: (context, state) => const HomeScreen(),
          ),

          // Trip Planning
          GoRoute(
            path: AppConstants.routeTripPlanning,
            builder: (context, state) => const TripPlanningScreen(),
          ),

          // Profile
          GoRoute(
            path: AppConstants.routeProfile,
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),

      // Auth routes
      GoRoute(
        path: AppConstants.routeLogin,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppConstants.routeRegister,
        builder: (context, state) => const RegisterScreen(),
      ),

      // Trip details
      GoRoute(
        path: '${AppConstants.routeTripDetails}/:id',
        builder: (context, state) {
          final tripId = state.pathParameters['id']!;
          return TripDetailsScreen(tripId: tripId);
        },
      ),

      // Flight booking
      GoRoute(
        path: AppConstants.routeFlightSearch,
        builder: (context, state) => const FlightSearchScreen(),
      ),
      GoRoute(
        path: '${AppConstants.routeFlightDetails}/:id',
        builder: (context, state) {
          final flightId = state.pathParameters['id']!;
          return FlightDetailsScreen(flightId: flightId);
        },
      ),

      // Hotel booking
      GoRoute(
        path: AppConstants.routeHotelSearch,
        builder: (context, state) => const HotelSearchScreen(),
      ),
      GoRoute(
        path: '${AppConstants.routeHotelDetails}/:id',
        builder: (context, state) {
          final hotelId = state.pathParameters['id']!;
          return HotelDetailsScreen(hotelId: hotelId);
        },
      ),

      // Settings
      GoRoute(
        path: AppConstants.routeSettings,
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
  );
}

// Scaffold with bottom navigation bar
class ScaffoldWithNavBar extends StatelessWidget {
  final Widget child;

  const ScaffoldWithNavBar({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _calculateSelectedIndex(context),
        onTap: (index) => _onItemTapped(index, context),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.explore),
            label: 'Plan Trip',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;
    if (location.startsWith(AppConstants.routeHome)) {
      return 0;
    }
    if (location.startsWith(AppConstants.routeTripPlanning)) {
      return 1;
    }
    if (location.startsWith(AppConstants.routeProfile)) {
      return 2;
    }
    return 0;
  }

  void _onItemTapped(int index, BuildContext context) {
    switch (index) {
      case 0:
        GoRouter.of(context).go(AppConstants.routeHome);
        break;
      case 1:
        GoRouter.of(context).go(AppConstants.routeTripPlanning);
        break;
      case 2:
        GoRouter.of(context).go(AppConstants.routeProfile);
        break;
    }
  }
}
