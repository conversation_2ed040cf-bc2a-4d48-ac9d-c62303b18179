import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'core/api/openrouter_api_service.dart';
import 'core/config/app_router.dart';
import 'core/theme/app_theme.dart';
import 'features/trip_planning/domain/repositories/trip_planning_repository.dart';
import 'features/trip_planning/data/repositories/trip_planning_repository_impl.dart';
import 'features/trip_planning/presentation/bloc/trip_planning_bloc.dart';

void main() async {
  // Load environment variables
  await dotenv.load(fileName: ".env");

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Create API service
    final apiService = OpenRouterApiService();

    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<OpenRouterApiService>(
          create: (context) => apiService,
        ),
        RepositoryProvider<TripPlanningRepository>(
          create:
              (context) => TripPlanningRepositoryImpl(apiService: apiService),
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider<TripPlanningBloc>(
            create:
                (context) => TripPlanningBloc(
                  repository: context.read<TripPlanningRepository>(),
                ),
          ),
        ],
        child: MaterialApp.router(
          title: 'AI Travel Planner',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          routerConfig: AppRouter.router,
          debugShowCheckedModeBanner: false,
        ),
      ),
    );
  }
}
